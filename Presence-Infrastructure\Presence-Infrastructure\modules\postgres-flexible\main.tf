data "azurerm_key_vault" "key_vault" {
  name                = var.key_valut_name
  resource_group_name = var.rg_name
}
data "azurerm_key_vault_secret" "db_username" {
  name         = var.db_username_secret
  key_vault_id = data.azurerm_key_vault.key_vault.id
}
data "azurerm_key_vault_secret" "db_password" {
  name         = var.db_password_secret
  key_vault_id = data.azurerm_key_vault.key_vault.id
}
resource "azurerm_private_dns_zone" "dnsZone" {
  count               = var.private_access_enabled ? 1 : 0
  name                = var.private_dns_zone_name
  resource_group_name = var.rg_name
}

resource "azurerm_private_dns_zone_virtual_network_link" "dnsZoneLink" {
  count = var.private_access_enabled ? 1 : 0
  name  = var.dnsZone_link_Name

  private_dns_zone_name = azurerm_private_dns_zone.dnsZone[count.index].name
  virtual_network_id    = var.vnet_id
  resource_group_name   = var.rg_name
}

resource "azurerm_postgresql_flexible_server" "postgres_DB" {
  name                          = var.db_name
  resource_group_name           = var.rg_name
  location                      = var.location
  version                       = var.db_version
  public_network_access_enabled = var.public_network_access_enabled
  administrator_login           = data.azurerm_key_vault_secret.db_username.value
  administrator_password        = data.azurerm_key_vault_secret.db_password.value
  zone                          = var.zone
  storage_mb                    = var.storage_mb
  storage_tier                  = var.storage_tier
  sku_name                      = var.sku_name
  delegated_subnet_id           = var.public_network_access_enabled ? null : var.subnet_id
  private_dns_zone_id           = var.public_network_access_enabled ? null : azurerm_private_dns_zone.dnsZone[0].id

}
