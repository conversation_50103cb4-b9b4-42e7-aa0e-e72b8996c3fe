variable "subnet_id" {
  description = "The ID of the subnet to be integrated with the web app"
  type        = string
}
variable "web_apps" {
  description = "List of web apps to be created"
  type = list(object({
    name = string
    cors_config = optional(object({
      allowed_origins     = list(string)
      support_credentials = bool
    }))
    app_settings = optional(map(string), {})
    sticky_settings = optional(object({
      app_setting_names     = optional(list(string), [])
      connection_string_names = optional(list(string), [])
    }), null)
  }))
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The Azure region"
  type        = string
}

variable "app_service_plan_name" {
  description = "The name of the App Service Plan"
  type        = string
}

variable "sku_name" {
  description = "The SKU name for the App Service Plan"
  type        = string
  default     = "S1"
}

variable "os_type" {
  description = "The OS type for the web app (Linux or Windows)"
  type        = string
  default     = "Windows"

}

variable "app_settings" {
  description = "Application settings for the web app"
  type        = map(string)
  default     = {}
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
