variable "application_insights_name" {
  description = "Name of the Application Insights resource"
  type        = string
}

variable "location" {
  description = "Azure region"
  type        = string
}

variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "application_type" {
  description = "Type of the application being monitored"
  type        = string
  default     = "web"
}

variable "workspace_id" {
  description = "Resource ID of the Log Analytics Workspace"
  type        = string
}

variable "tags" {
  description = "Tags to apply to Application Insights"
  type        = map(string)
  default     = {}
}
