name: Terraform-CI-Pipeline

trigger:
  branches:
    include:
      - master

pr:
  branches:
    include:
      - master

pool:
  vmImage: 'ubuntu-22.04'

parameters:
  - name: environment
    type: string
    default: dev
    values:
      - dev
      - qa
      - prod

variables:
  - name: environment
    value: ${{ parameters.environment }}
  - name: TF_VERSION
    value: '1.6.1'
  - name: WORKING_DIR
    value: '$(System.DefaultWorkingDirectory)'
  - name: serviceConnection
    ${{ if eq(parameters.environment, 'prod') }}:
      value: AzureDevops-Pipelines-Prod
    ${{ if eq(parameters.environment, 'qa') }}:
      value: AzureDevops-Pipelines
    ${{ if eq(parameters.environment, 'dev') }}:
      value: AzureDevops-Pipelines  
  - name: storageAccountName
    ${{ if eq(parameters.environment, 'prod') }}:
      value: arazest1tfstateprod
    ${{ if eq(parameters.environment, 'qa') }}:
      value: arazest1tfstateqa
    ${{ if eq(parameters.environment, 'dev') }}:
      value: arazest1tfstatedev
  - stage: DetectChanges
    jobs:
      - job: IdentifyChangedWorkspaces
        steps:
          - checkout: self
            fetchDepth: 0

          - bash: |
              changed_files=$(git diff --name-only HEAD^ HEAD)
              declare -A workspace_set
              has_workspace='false'

              while IFS= read -r file; do
                if [[ "$file" =~ ^environments/([^/]+)/workspaces/([^/]+)/ ]]; then
                  env="${BASH_REMATCH[1]}"
                  ws="${BASH_REMATCH[2]}"
                  if [ -d "environments/$env/workspaces/$ws" ]; then
                    key="${env}/${ws}"
                    workspace_set["$key"]="$env:$ws"
                    has_workspace='true'
                  fi
                fi
              done <<< "$changed_files"

              if echo "$changed_files" | grep -q "^modules/"; then
                for env_dir in environments/*; do
                  if [ -d "$env_dir" ]; then
                    for ws_dir in "$env_dir"/workspaces/*; do
                      if [ -d "$ws_dir" ] && [ -f "$ws_dir/main.tf" ]; then
                        if grep -q "module.*source.*modules/" "$ws_dir/main.tf"; then
                          env=$(basename "$env_dir")
                          ws=$(basename "$ws_dir")
                          key="${env}/${ws}"
                          workspace_set["$key"]="$env:$ws"
                          has_workspace='true'
                        fi
                      fi
                    done
                  fi
                done
              fi

              json_object="{"
              first=true
              KEYS=$(echo "${!workspace_set[@]}" | tr ' ' '\012' | sort | tr '\012' ' ')
              for key in ${KEYS}; do
                if [ "$first" = true ]; then
                  first=false
                else
                  json_object="$json_object,"
                fi
                IFS=":" read env ws <<< "${workspace_set[$key]}"
                json_object="$json_object\"$key\":{\"environment\":\"$env\",\"workspace\":\"$ws\"}"
              done
              json_object="$json_object}"

              echo "##vso[task.setvariable variable=HAS_WORKSPACE;isOutput=true]$has_workspace"
              echo "##vso[task.setvariable variable=WORKSPACE_MATRIX;isOutput=true]$json_object"

              echo "$json_object"
              echo "Has workspace: $has_workspace"
            name: detectChanges

  - stage: Validate
    dependsOn: DetectChanges
    condition: eq(dependencies.DetectChanges.outputs['IdentifyChangedWorkspaces.detectChanges.HAS_WORKSPACE'], 'true')
    jobs:
      - job: Validate_Workspaces
        variables:
          matrix: $[ stageDependencies.DetectChanges.IdentifyChangedWorkspaces.outputs['detectChanges.WORKSPACE_MATRIX'] ]
        strategy:
          matrix: ${{ variables.matrix }}
        steps:
          - checkout: self

          - bash: |
              echo "Environment: $(environment)"
              echo "Workspace: $(workspace)"
            displayName: 'Print Environment and Workspace'

          - bash: |
              if [ ! -d "environments/$(environment)/workspaces/$(workspace)" ]; then
                echo "##vso[task.logissue type=error]Workspace $(workspace) does not exist in environment $(environment)."
                echo "##vso[task.complete result=Failed;]"
              else
                echo "Workspace $(workspace) exists in environment $(environment). Proceeding with validation."
              fi
            displayName: 'Validate Workspace Exists'

          - task: UsePythonVersion@0
            inputs:
              versionSpec: '3.x'
              addToPath: true

          - script: |
              curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
              sudo apt-add-repository "deb https://apt.releases.hashicorp.com $(lsb_release -cs) main"
              sudo apt-get update && sudo apt-get install terraform
              terraform --version
            displayName: 'Install Terraform'

          - script: |
              curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash
            displayName: 'Install TFLint'

          - script: |
              tflint --init && \
              tflint --config $(WORKING_DIR)/.tflint.hcl --chdir=$(WORKING_DIR)/environments/$(environment)/workspaces/$(workspace) || exit 1
            displayName: 'Run TFLint'

          - script: |
              pip install pre-commit
              pre-commit run --config .pre-commit-config.yml --files environments/$(environment)/workspaces/$(workspace)/*
            displayName: 'Run pre-commit'
            continueOnError: false

          - task: TerraformInstaller@0
            inputs:
              terraformVersion: $(TF_VERSION)

          - task: TerraformTaskV3@3
            name: terraformInit
            inputs:
              provider: 'azurerm'
              command: 'init'
              workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
              backendServiceArm: $(serviceConnection)
              backendAzureRmResourceGroupName: 'ar-az-est1-tfstate-rg'
              backendAzureRmStorageAccountName: arazest1tfstate$(environment)
              backendAzureRmContainerName: 'tfstate'
              backendAzureRmKey: 'terraform.tfstate'

          - task: TerraformTaskV3@3
            name: terraformSelectWorkspace
            inputs:
              provider: 'azurerm'
              command: 'custom'
              customCommand: 'workspace'
              commandOptions: 'select -or-create $(workspace)'
              workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
              environmentServiceNameAzureRM: $(serviceConnection)

          - task: TerraformTaskV3@3
            name: terraformValidate
            inputs:
              provider: 'azurerm'
              command: 'validate'
              workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
              environmentServiceNameAzureRM: $(serviceConnection)

          - task: TerraformTaskV3@3
            name: terraformPlan
            inputs:
              provider: 'azurerm'
              command: 'plan'
              workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
              environmentServiceNameAzureRM: $(serviceConnection)
              publishPlanResults: '$(workspace)-plan'

  - stage: Approval
    displayName: 'Manual Approval'
    pool: server
    condition: always()
    jobs:
      - job: WaitForApproval
        steps:
          - task: ManualValidation@0
            inputs:
              notifyUsers: |
                <EMAIL>
              instructions: 'Please validate the merge for $(environment) before proceeding with the deployment.'
              onTimeout: 'reject'
