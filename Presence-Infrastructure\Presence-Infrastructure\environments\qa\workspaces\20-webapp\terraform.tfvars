subnet_id           = "/subscriptions/91c43315-5ccf-47d1-85dc-c73e8579b055/resourceGroups/ar-az-est1-q-cps-rg-001/providers/Microsoft.Network/virtualNetworks/ar-az-est1-q-cps-vnet-001/subnets/ar-az-est1-q-cps-sn-001"
service_plan_name   = "ar-az-est1-q-cps-asp-001" # App Service Plan name
sku_name            = "B2"
resource_group_name = "ar-az-est1-q-cps-rg-001"
location            = "eastus"
environment         = "qa"
os_type             = "Windows"
web_apps = [
  {
    name = "ar-az-est1-q-cpsadmin-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1qadminst001.z13.web.core.windows.net",
        "https://qa-app.ecps.ca"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://administrationportal/api"
    }
  },
  {
    name = "ar-az-est1-q-cpssupplier-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1qsupplierst001.z13.web.core.windows.net",
        "https://qa-supplier.ecps.ca"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://supplierportal/api"
    }
  }
]

storage_accounts = [
  {
    name                          = "arazest1qadminst001"
    resource_group_name           = "ar-az-est1-q-cps-rg-001"
    location                      = "eastus"
    account_tier                  = "Standard"
    static_website_index_document = "index.html"
    account_replication_type      = "LRS"
    tags = {
      accountnumber = ""
      criticality   = "high"
      environment   = "qa"
      owner         = "<EMAIL>"
      profitcenter  = ""
      project       = "presence"
      terraform     = "true"
    }
    }, {
    name                          = "arazest1qsupplierst001"
    resource_group_name           = "ar-az-est1-q-cps-rg-001"
    location                      = "eastus"
    account_tier                  = "Standard"
    static_website_index_document = "index.html"
    account_replication_type      = "LRS"
    tags = {
      accountnumber = ""
      criticality   = "high"
      environment   = "qa"
      owner         = "<EMAIL>"
      profitcenter  = ""
      project       = "presence"
      terraform     = "true"
    }
  }
]

tags = {
  accountnumber = ""
  criticality   = "high"
  environment   = "qa"
  owner         = "<EMAIL>"
  profitcenter  = ""
  project       = "presence"
}
