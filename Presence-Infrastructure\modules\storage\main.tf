resource "azurerm_storage_account" "main" {
  for_each = { for sa in var.storage_accounts : sa.name => sa }

  name                     = each.value.name
  resource_group_name      = each.value.resource_group_name
  location                 = each.value.location
  account_tier             = each.value.account_tier
  account_replication_type = each.value.account_replication_type
  tags                     = each.value.tags
}

resource "azurerm_storage_account_static_website" "main" {
  for_each = {
    for sa in var.storage_accounts : sa.name => sa
    if lookup(sa, "static_website_index_document", null) != null
  }

  storage_account_id = azurerm_storage_account.main[each.key].id
  index_document     = each.value.static_website_index_document
  error_404_document = each.value.static_website_index_document
}
