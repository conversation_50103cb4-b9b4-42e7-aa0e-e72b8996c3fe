output "postgresql_server_name" {
  description = "The name of the PostgreSQL Flexible Server"
  value       = azurerm_postgresql_flexible_server.postgres_DB.name
}

output "postgresql_fqdn" {
  description = "The fully qualified domain name of the PostgreSQL server"
  value       = azurerm_postgresql_flexible_server.postgres_DB.fqdn
}
output "private_dns_zone_name" {
  description = "The private DNS zone name used for PostgreSQL"
value = var.private_access_enabled ? azurerm_private_dns_zone.dnsZone[0].name : "N/A"
}
