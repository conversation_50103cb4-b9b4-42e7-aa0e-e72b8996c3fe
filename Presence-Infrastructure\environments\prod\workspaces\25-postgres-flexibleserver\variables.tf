variable "db_name" {
  type        = string
  description = "Name of the Postgres Flexible Database Server"
}
variable "key_valut_name" {
  type        = string
  description = "Key Vault Name"

}
variable "db_username_secret" {
  type        = string
  description = "Db username id in Keyvault"

}
variable "db_password_secret" {
  type        = string
  description = "Db username id in Keyvault"

}
variable "private_access_enabled" {
  type        = bool
  description = "Private Acces Enable or Disable"
}
variable "dnsZone_link_Name" {
  type        = string
  description = "Private DNS Zone Link Name"
}
variable "public_network_access_enabled" {
  type        = bool
  description = "Public or Private Access to Database"
}
variable "subnet_id" {
  type        = number
  description = "Subnet Id for Private Access"
}
variable "location" {
  type        = string
  description = "Location Of Database Where to Deploy"
}
variable "rg_name" {
  type        = string
  description = "Resource Group Name"
}

variable "db_version" {
  type        = string
  description = "Database Version"

}
variable "storage_mb" {
  type        = number
  description = "Storage Size"

}
variable "storage_tier" {
  type        = string
  description = "Database Storage Tier"

}
variable "sku_name" {
  type        = string
  description = "Database sku_name"

}
variable "zone" {
  type        = number
  description = "Zone Numbers"

}
variable "private_dns_zone_name" {
  description = "The name of the private DNS zone"
  type        = string
}
variable "vnet_id" {
  description = "Vnet ID"
  type        = string
}