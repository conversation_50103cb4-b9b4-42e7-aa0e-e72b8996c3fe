location             = "eastus"
virtual_network_name = "ar-az-est1-d-presence-vnet-001"
address_space        = ["***********/27"]
resource_group_name  = "ar-az-est1-d-presence-rg-001"
subnets = [
  {
    name           = "ar-az-est1-presence-sn-001"
    address_prefix = "***********/28"
    delegation     = "Microsoft.Web/serverFarms"
  },
  {
    name           = "ar-az-est1-presence-sn-002"
    address_prefix = "***********/28"
  },
]

nsg_configs = []

tags = {
  accountnumber = ""
  environment   = "dev"
  owner         = "<EMAIL>"
  profitcenter  = ""
  project       = "presence"
  terraform     = "true"
}
