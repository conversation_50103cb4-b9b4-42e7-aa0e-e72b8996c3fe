resource "azurerm_service_plan" "main" {
  os_type             = var.os_type
  name                = var.app_service_plan_name
  location            = var.location
  resource_group_name = var.resource_group_name
  sku_name            = var.sku_name
  tags                = var.tags
}

resource "azurerm_windows_web_app" "main" {
  for_each                  = { for app in var.web_apps : app.name => app }
  name                      = each.value.name
  resource_group_name       = var.resource_group_name
  location                  = var.location
  service_plan_id           = azurerm_service_plan.main.id
  tags                      = var.tags
  virtual_network_subnet_id = var.subnet_id

  site_config {
    always_on = true
    application_stack {
      dotnet_version = "v8.0"
      current_stack  = "dotnet"
    }
    vnet_route_all_enabled = true

    cors {
      allowed_origins     = each.value.cors_config != null ? each.value.cors_config.allowed_origins : ["https://arazest1dadminst001.z13.web.core.windows.net", "https://arazest1dsupplierst001.z13.web.core.windows.net"]
      support_credentials = each.value.cors_config != null ? each.value.cors_config.support_credentials : true
    }
  }

  identity {
    type = "SystemAssigned"
  }

  app_settings = merge(var.app_settings, each.value.app_settings)

  # Ignore changes to app_settings and tags to prevent Terraform from overwriting sticky settings managed in the Azure portal
  lifecycle {
    ignore_changes = [app_settings, tags, sticky_settings]
  }
}
