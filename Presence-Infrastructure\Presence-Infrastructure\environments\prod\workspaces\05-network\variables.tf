variable "virtual_network_name" {
  description = "Name of the virtual network"
  type        = string
}

variable "address_space" {
  description = "Address space for the virtual network"
  type        = list(string)
}



variable "location" {
  description = "The Azure location where the resources will be created."
  type        = string
  default     = "East US"
}

variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "subnets" {
  description = "List of subnet configurations"
  type = list(object({
    name              = string
    address_prefix    = string
    delegation        = optional(string)
    service_endpoints = optional(list(string))
  }))
}

variable "nsg_configs" {
  description = "List of NSG configurations"
  type = list(object({
    nsg_name            = string
    location            = string
    resource_group_name = string
    security_rules = list(object({
      name                       = string
      priority                   = number
      direction                  = string
      access                     = string
      protocol                   = string
      source_port_range          = string
      destination_port_range     = string
      source_address_prefix      = string
      destination_address_prefix = string
    }))
  }))

}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to the resources"
  default     = {}
}
