name: Terraform-CD-Pipeline

trigger: none

pr: none

pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: environment
    type: string
    default: dev
    values:
      - dev
      - qa
      - prod

variables:
  - group: terraform-var-group
  - name: environment
    value: ${{ parameters.environment }}
  - name: TF_VERSION
    value: '1.6.1'
  - name: WORKING_DIR
    value: '$(System.DefaultWorkingDirectory)'
  - name: serviceConnection
    ${{ if eq(parameters.environment, 'prod') }}:
      value: AzureDevops-Pipelines-Prod
    ${{ if eq(parameters.environment, 'qa') }}:
      value: AzureDevops-Pipelines
    ${{ if eq(parameters.environment, 'dev') }}:
      value: AzureDevops-Pipelines

stages:
- stage: DetectChanges
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
  jobs:
  - job: IdentifyChangedWorkspaces
    steps:
    - checkout: self
      fetchDepth: 0

    - bash: |
        # Get last successful deployment commit
        LAST_DEPLOY_TAG="last_deploy_${{ parameters.environment }}"

        # Check if deployment tag exists
        if git rev-parse "$LAST_DEPLOY_TAG" >/dev/null 2>&1; then
          LAST_COMMIT=$(git rev-parse "$LAST_DEPLOY_TAG")
        else
          # If no previous deployment, get the first commit
          LAST_COMMIT=$(git rev-list --max-parents=0 HEAD)
        fi

        # Get changed files between the last two commits
        changed_files=$(git diff --name-only $LAST_COMMIT HEAD)

        # Use an associative array to track unique environment/workspace combinations.
        declare -A workspace_set

        # Flag to indicate if any workspace is affected
        has_workspace='false'

        # Process changed files to detect direct workspace changes.
        # This will match paths like: environments/<env>/workspaces/<workspace>/...
        while IFS= read -r file; do
          if [[ "$file" =~ ^environments/${{ parameters.environment }}/workspaces/([^/]+)/ ]]; then
            env="${{parameters.environment}}"
            ws="${BASH_REMATCH[1]}"
            # Check if the workspace directory actually exists
            if [ -d "environments/$env/workspaces/$ws" ]; then
              key="${env}/${ws}"
              # Insert into associative array; duplicate keys will be overwritten
              workspace_set["$key"]="$env:$ws"
              has_workspace='true'
            else
              echo "Warning: Referenced workspace $ws does not exist, skipping."
            fi
          fi
        done <<< "$changed_files"

        # If any file under modules/ changed, check all workspaces for module references.
        if echo "$changed_files" | grep -q "^modules/"; then
          # Loop through each workspace within the environment
          for ws_dir in environments/"${{parameters.environment}}"/workspaces/*; do
            if [ -d "$ws_dir" ] && [ -f "$ws_dir/main.tf" ]; then
              # If the workspace's main.tf references a module from modules/
              if grep -q "module.*source.*modules/" "$ws_dir/main.tf"; then
                env=${{parameters.environment}}
                ws=$(basename "$ws_dir")
                # Double-check that the workspace directory exists (should always be true here, but being cautious)
                if [ -d "environments/$env/workspaces/$ws" ]; then
                  key="${env}/${ws}"
                  workspace_set["$key"]="$env:$ws"
                  has_workspace='true'
                else
                  echo "Warning: Workspace directory $ws unexpectedly does not exist, skipping."
                fi
              fi
            fi
          done
        fi

        # Build the JSON object using only the unique keys from the associative array.
        json_object="{"
        first=true
        KEYS=$(echo "${!workspace_set[@]}" |  tr ' ' '\012' | sort | tr '\012' ' ')
        for key in ${KEYS}; do
          if [ "$first" = true ]; then
            first=false
          else
            json_object="$json_object,"
          fi
          # Split the stored value (format "env:workspace") into separate variables.
          IFS=":" read env ws <<< "${workspace_set[$key]}"
          json_object="$json_object\"$key\":{\"environment\":\"$env\",\"workspace\":\"$ws\"}"
        done
        json_object="$json_object}"

        # Set Azure DevOps variables using logging commands.
        echo "##vso[task.setvariable variable=HAS_WORKSPACE;isOutput=true]$has_workspace"
        echo "##vso[task.setvariable variable=WORKSPACE_MATRIX;isOutput=true]$json_object"

        # Save the current commit hash for updating the tag later
        echo "##vso[task.setvariable variable=CURRENT_COMMIT;isOutput=true]$(git rev-parse HEAD)"

        # Log the JSON and flag.
        echo "$json_object"
        echo "Has workspace: $has_workspace"
      name: detectChanges

- stage: Apply
  dependsOn:
  - DetectChanges
  condition: and(succeeded(), eq(dependencies.DetectChanges.outputs['IdentifyChangedWorkspaces.detectChanges.HAS_WORKSPACE'], 'true'))
  jobs:
  - job: Deploy_Workspaces
    displayName: Deploy Changed Workspaces
    variables:
      workspace_matrix: $[ stageDependencies.DetectChanges.IdentifyChangedWorkspaces.outputs['detectChanges.WORKSPACE_MATRIX'] ]
    strategy:
      matrix: $[ variables.workspace_matrix ]
    steps:
      - checkout: self

      - bash: |
          echo "Environment: $(environment)"
          echo "Workspace: $(workspace)"
        displayName: 'Print Environment and Workspace'

      - bash: |
          if [ ! -d "environments/$(environment)/workspaces/$(workspace)" ]; then
            echo "##vso[task.logissue type=error]Workspace $(workspace) does not exist in environment $(environment)."
            echo "##vso[task.complete result=Failed;]"
          else
            echo "Workspace $(workspace) exists in environment $(environment). Proceeding with deployment."
          fi
        displayName: 'Validate Workspace Exists'
        condition: succeeded()

      - task: TerraformInstaller@0
        inputs:
          terraformVersion: $(TF_VERSION)

      - task: TerraformTaskV3@3
        name: terraformInit
        inputs:
          provider: 'azurerm'
          command: 'init'
          workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
          backendServiceArm: $(serviceConnection)
          backendAzureRmResourceGroupName: 'ar-az-est1-tfstate-rg'
          backendAzureRmStorageAccountName: arazest1tfstate$(environment)
          backendAzureRmContainerName: 'tfstate'
          backendAzureRmKey: 'terraform.tfstate'

      - task: TerraformTaskV3@3
        name: terraformSelectWorkspace
        inputs:
          provider: 'azurerm'
          command: 'custom'
          customCommand: 'workspace'
          commandOptions: 'select -or-create $(workspace)'
          workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
          environmentServiceNameAzureRM: ${{ variables.serviceConnection }}

      - task: TerraformTaskV3@3
        name: terraformPlan
        inputs:
          provider: 'azurerm'
          command: 'plan'
          workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
          environmentServiceNameAzureRM: ${{ variables.serviceConnection }}
          publishPlanResults: '$(workspace)-plan'

      - task: TerraformTaskV3@3
        name: terraformApply
        inputs:
          provider: 'azurerm'
          command: 'apply'
          workingDirectory: 'environments/${{ parameters.environment }}/workspaces/$(workspace)'
          environmentServiceNameAzureRM: ${{ variables.serviceConnection }}
          commandOptions: '-auto-approve'

- stage: UpdateDeployTag
  dependsOn:
  - Apply
  condition: succeeded()
  jobs:
  - job: TagSuccessfulDeployment
    variables:
      - name: CURRENT_COMMIT
        value: $[ dependencies.DetectChanges.outputs['IdentifyChangedWorkspaces.detectChanges.CURRENT_COMMIT'] ]
    steps:
    - checkout: self
      persistCredentials: true

    - bash: |
        # Configure git for Azure DevOps
        git config --global user.email "<EMAIL>"
        git config --global user.name "Azure DevOps Pipeline"

        # Update or create the deployment tag
        git tag -f "last_deploy_${{ parameters.environment }}" $(CURRENT_COMMIT)
        git push origin "last_deploy_${{ parameters.environment }}" --force
      name: updateTag