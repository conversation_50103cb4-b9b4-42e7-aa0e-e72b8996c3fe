location             = "eastus"
virtual_network_name = "ar-az-est1-p-presence-vnet-001"
address_space        = ["10.0.0.0/16"]
resource_group_name  = "ar-az-est1-p-presence-rg-001"
subnets = [
  {
    name           = "ar-az-est1-p-presence-sn-001"
    address_prefix = "********/24"
    delegation     = "Microsoft.Web/serverFarms"
  },
  {
    name           = "ar-az-est1-p-presence-sn-002"
    address_prefix = "********/26"
    delegation     = "Microsoft.DBforPostgreSQL/flexibleServers"

  },
]

nsg_configs = []

tags = {
  accountnumber = ""
  environment   = "prod"
  owner         = "<EMAIL>"
  profitcenter  = ""
  project       = "presence"
}
