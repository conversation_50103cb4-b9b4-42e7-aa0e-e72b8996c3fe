subnet_id           = "/subscriptions/16ccf387-4430-45f8-b1b9-561222c09620/resourceGroups/ar-az-est1-p-cps-rg-001/providers/Microsoft.Network/virtualNetworks/ar-az-est1-p-cps-vnet-001/subnets/ar-az-est1-p-cps-sn-001"
service_plan_name   = "ar-az-est1-p-presence-asp-001" # App Service Plan name
sku_name            = "B2"
resource_group_name = "ar-az-est1-p-presence-rg-001"
location            = "eastus"
environment         = "prod"
os_type             = "Windows"
web_apps = [
  {
    name = "ar-az-est1-p-cpsadmin-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1padminst001.z13.web.core.windows.net",
        "https://qa-app.ecps.ca"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://administrationportal/api"
    }
  },
  {
    name = "ar-az-est1-p-cpssupplier-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1psupplierst001.z13.web.core.windows.net",
        "https://qa-supplier.ecps.ca"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://supplierportal/api"
    }
  }
]

storage_accounts = [
  {
    name                              = "arazest1padminst001"
    resource_group_name               = "ar-az-est1-p-cps-rg-001"
    location                          = "eastus"
    account_tier                      = "Standard"
    static_website_index_document     = "index.html"
    static_website_error_404_document = "index.html"
    account_replication_type          = "LRS"
    tags = {
      accountnumber = ""
      criticality   = "high"
      environment   = "prod"
      owner         = "<EMAIL>"
      profitcenter  = ""
      project       = "presence"
      terraform     = "true"
    }
    }, {
    name                              = "arazest1psupplierst001"
    resource_group_name               = "ar-az-est1-p-cps-rg-001"
    location                          = "eastus"
    account_tier                      = "Standard"
    static_website_index_document     = "index.html"
    static_website_error_404_document = "index.html"
    account_replication_type          = "LRS"
    tags = {
       accountnumber = ""
      criticality   = "high"
      environment   = "prod"
      owner         = "<EMAIL>"
      profitcenter  = ""
      project       = "presence"
      terraform     = "true"
    }
  }
]

tags = {
  accountnumber = ""
      criticality   = "high"
      environment   = "prod"
      owner         = "<EMAIL>"
      profitcenter  = ""
      project       = "presence"
}
