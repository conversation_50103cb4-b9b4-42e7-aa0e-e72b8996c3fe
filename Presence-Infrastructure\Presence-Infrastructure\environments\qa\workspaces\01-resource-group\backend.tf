# environments/qa/workspaces/01-resource-group/backend.tf
terraform {
  backend "azurerm" {
    resource_group_name  = "ar-az-est1-tfstate-rg"
    storage_account_name = "arazest1tfstateqa"
    container_name       = "tfstate"
    key                  = "terraform.tfstate"
  }

  required_version = ">= 1.6.0"

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}
