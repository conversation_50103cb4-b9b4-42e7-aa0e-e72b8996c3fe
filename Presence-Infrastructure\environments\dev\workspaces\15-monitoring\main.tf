module "log_analytics" {
  source = "../../../../modules/log-analytics"

  log_analytics_workspace_name = var.log_analytics_workspace_name
  location                     = var.location
  resource_group_name          = var.resource_group_name
  sku                          = var.log_analytics_sku
  retention_in_days            = var.retention_in_days
  tags                         = var.tags
}

module "application_insights" {
  source = "../../../../modules/application-insights"

  application_insights_name = var.application_insights_name
  location                  = var.location
  resource_group_name       = var.resource_group_name
  application_type          = "web"
  workspace_id              = module.log_analytics.id
  tags                      = var.tags

  depends_on = [module.log_analytics]
}
