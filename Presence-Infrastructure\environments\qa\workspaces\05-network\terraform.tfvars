location             = "eastus"
virtual_network_name = "ar-az-est1-q-presence-vnet-001"
address_space        = ["************/27"]
dns_servers          = ["***********", "***********"]
resource_group_name  = "ar-az-est1-q-presence-rg-001"
subnets = [
  {
    name           = "ar-az-est1-q-presence-sn-001"
    address_prefix = "************/28"
    delegation     = "Microsoft.Web/serverFarms"
  },
  {
    name           = "ar-az-est1-q-presence-sn-002"
    address_prefix = "************/28"
  },
]

nsg_configs = []

tags = {
  accountnumber = ""
  environment   = "qa"
  owner         = "<EMAIL>"
  profitcenter  = ""
  project       = "presence"
}
