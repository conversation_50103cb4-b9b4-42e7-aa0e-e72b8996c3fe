variable "resource_group_name" {
  type        = string
  description = "Name of the resource group"
}

variable "virtual_network_name" {
  type        = string
  description = "Name of the virtual network"
}

variable "location" {
  type        = string
  description = "Azure region where the virtual network will be created"
}

variable "address_space" {
  type        = list(string)
  description = "List of address spaces for the virtual network"
}



variable "subnets" {
  type = list(object({
    name              = string
    address_prefix    = string
    delegation        = optional(string)
    service_endpoints = optional(list(string))
  }))
  description = "List of subnet configurations"
  default = [
    {
       name           = "ar-az-est1-presence-sn-001"
      address_prefix = "********/27"
    },
    {
      name           = "ar-az-est1-presence-sn-002"
      address_prefix = "*********/27"
    }
  ]
}


variable "nsg_configs" {
  description = "List of NSG configurations"
  type = list(object({
    nsg_name            = string
    location            = string
    resource_group_name = string
    security_rules = list(object({
      name                       = string
      priority                   = number
      direction                  = string
      access                     = string
      protocol                   = string
      source_port_range          = string
      destination_port_range     = string
      source_address_prefix      = string
      destination_address_prefix = string
    }))
  }))
}


variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
